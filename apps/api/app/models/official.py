# app/models/official.py
from sqlalchemy import Column, String, Text, <PERSON><PERSON><PERSON>, Integer, Enum as SQLEnum
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from app.db.base_class import Base
from app.db.types import get_json_type
import enum

class OfficialLevel(str, enum.Enum):
    FEDERAL = "federal"
    STATE = "state"
    LOCAL = "local"

class OfficialChamber(str, enum.Enum):
    HOUSE = "house"
    SENATE = "senate"
    EXECUTIVE = "executive"
    JUDICIAL = "judicial"
    OTHER = "other"

class Official(Base):
    __tablename__ = "officials"
    
    # Basic official information
    name = Column(String, nullable=False, index=True)
    title = Column(String, nullable=False)
    party = Column(String, nullable=True)
    
    # Contact information
    email = Column(String, nullable=True)
    phone = Column(String, nullable=True)
    website = Column(String, nullable=True)
    
    # Address information
    office_address = Column(Text, nullable=True)
    office_city = Column(String, nullable=True)
    office_state = Column(String, nullable=True)
    office_zip = Column(String, nullable=True)
    
    # Government structure
    level = Column(SQLEnum(OfficialLevel), nullable=False)
    chamber = Column(SQLEnum(OfficialChamber), nullable=True)
    
    # Geographic representation
    state = Column(String, nullable=True)  # State abbreviation
    district = Column(String, nullable=True)  # District number or name
    
    # External identifiers
    bioguide_id = Column(String, nullable=True, unique=True, index=True)
    openstates_id = Column(String, nullable=True, unique=True, index=True)
    google_civic_id = Column(String, nullable=True, unique=True, index=True)
    
    # Social media
    twitter_handle = Column(String, nullable=True)
    facebook_url = Column(String, nullable=True)
    instagram_handle = Column(String, nullable=True)
    
    # Official profile
    bio = Column(Text, nullable=True)
    profile_picture_url = Column(String, nullable=True)
    
    # Term information
    term_start = Column(String, nullable=True)  # Year as string
    term_end = Column(String, nullable=True)    # Year as string
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Voting record and positions
    voting_record = Column(get_json_type(), nullable=True)  # Array of vote records
    positions = Column(get_json_type(), nullable=True)      # Array of position statements
    
    # Engagement metrics
    response_rate = Column(Integer, nullable=True)  # Response rate percentage
    avg_response_time = Column(Integer, nullable=True)  # Response time in hours
    
    # Metadata
    official_metadata = Column(get_json_type(), nullable=True)
    
    # Relationships
    # actions = relationship("Action", back_populates="official", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Official(name='{self.name}', title='{self.title}', state='{self.state}')>"
    
    @property
    def full_title(self) -> str:
        if self.state:
            return f"{self.title} from {self.state}"
        return self.title
    
    @property
    def display_name(self) -> str:
        if self.party:
            return f"{self.name} ({self.party})"
        return self.name
    
    @property
    def contact_preference(self) -> str:
        """Return the preferred contact method"""
        if self.email:
            return "email"
        elif self.phone:
            return "phone"
        elif self.website:
            return "website"
        return "none"
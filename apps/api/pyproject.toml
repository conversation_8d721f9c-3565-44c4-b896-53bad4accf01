[project]
name = "modernaction-api"
version = "0.1.0"
description = "ModernAction.io API Backend"
authors = [
    {name = "ModernAction Team"}
]
readme = "README.md"
requires-python = "^3.9"
dependencies = [
    "fastapi (>=0.116.1,<0.117.0)",
    "uvicorn (>=0.35.0,<0.36.0)",
    "pydantic[email] (>=2.11.7,<3.0.0)",
    "python-dotenv (>=1.1.1,<2.0.0)",
    "sqlalchemy (>=2.0.0,<3.0.0)",
    "alembic (>=1.13.0,<2.0.0)",
    "psycopg2-binary (>=2.9.0,<3.0.0)",
    "asyncpg (>=0.28.0,<1.0.0)",
    "redis (>=5.0.0,<6.0.0)",
    "celery (>=5.3.0,<6.0.0)",
    "passlib[bcrypt] (>=1.7.4,<2.0.0)",
    "python-jose[cryptography] (>=3.3.0,<4.0.0)",
    "python-multipart (>=0.0.6,<1.0.0)",
    "aiofiles (>=23.2.0,<24.0.0)",
    "transformers (>=4.30.0,<5.0.0)",
    "torch (>=2.0.0,<3.0.0)",
    "requests (>=2.31.0,<3.0.0)",
    "pydantic-settings (>=2.0.0,<3.0.0)",
    "boto3 (>=1.26.0,<2.0.0)",
    "slowapi (>=0.1.9,<1.0.0)"
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.group.dev.dependencies]
pytest = "^8.4.1"
httpx = "^0.28.1"
pytest-asyncio = "^0.21.0"
pytest-cov = "^4.1.0"
black = "^23.0.0"
ruff = "^0.1.0"
mypy = "^1.5.0"
pre-commit = "^3.4.0"
factory-boy = "^3.3.0"
faker = "^19.0.0"


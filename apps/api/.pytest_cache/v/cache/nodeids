["tests/test_config.py::TestConfiguration::test_api_key_settings", "tests/test_config.py::TestConfiguration::test_boolean_settings", "tests/test_config.py::TestConfiguration::test_case_sensitivity", "tests/test_config.py::TestConfiguration::test_cors_origins_list", "tests/test_config.py::TestConfiguration::test_database_url_setting", "tests/test_config.py::TestConfiguration::test_default_settings", "tests/test_config.py::TestConfiguration::test_environment_variable_override", "tests/test_config.py::TestConfiguration::test_global_settings_instance", "tests/test_config.py::TestConfiguration::test_integer_settings", "tests/test_config.py::TestConfiguration::test_optional_settings", "tests/test_config.py::TestConfiguration::test_production_settings", "tests/test_config.py::TestConfiguration::test_redis_url_setting", "tests/test_config.py::TestConfiguration::test_ses_settings", "tests/test_database.py::TestDatabaseConnection::test_database_connection", "tests/test_database.py::TestDatabaseConnection::test_database_foreign_key_constraints", "tests/test_database.py::TestDatabaseConnection::test_database_rollback", "tests/test_database.py::TestDatabaseConnection::test_database_session_isolation", "tests/test_database.py::TestDatabaseConnection::test_database_table_creation", "tests/test_database.py::TestDatabaseConnection::test_database_transaction", "tests/test_health.py::test_health_check", "tests/test_health.py::test_health_check_cors", "tests/test_health.py::test_health_check_headers", "tests/test_health.py::test_health_check_with_test_client", "tests/test_models.py::TestActionModel::test_action_properties", "tests/test_models.py::TestActionModel::test_create_action", "tests/test_models.py::TestBillModel::test_bill_enum_values", "tests/test_models.py::TestBillModel::test_bill_full_id_property", "tests/test_models.py::TestBillModel::test_create_bill", "tests/test_models.py::TestCampaignModel::test_campaign_completion_percentage", "tests/test_models.py::TestCampaignModel::test_campaign_properties", "tests/test_models.py::TestCampaignModel::test_create_campaign", "tests/test_models.py::TestModelRelationships::test_bill_campaigns_relationship", "tests/test_models.py::TestModelRelationships::test_user_actions_relationship", "tests/test_models.py::TestOfficialModel::test_create_official", "tests/test_models.py::TestOfficialModel::test_official_contact_preference", "tests/test_models.py::TestOfficialModel::test_official_properties", "tests/test_models.py::TestUserModel::test_create_user", "tests/test_models.py::TestUserModel::test_user_full_name_property", "tests/test_models.py::TestUserModel::test_user_unique_email", "tests/test_officials_api.py::TestOfficialsAPI::test_create_official", "tests/test_officials_api.py::TestOfficialsAPI::test_create_official_duplicate_external_id", "tests/test_officials_api.py::TestOfficialsAPI::test_delete_official", "tests/test_officials_api.py::TestOfficialsAPI::test_get_official_by_external_id", "tests/test_officials_api.py::TestOfficialsAPI::test_get_official_by_id", "tests/test_officials_api.py::TestOfficialsAPI::test_get_official_not_found", "tests/test_officials_api.py::TestOfficialsAPI::test_get_officials_by_chamber_endpoint", "tests/test_officials_api.py::TestOfficialsAPI::test_get_officials_by_level_endpoint", "tests/test_officials_api.py::TestOfficialsAPI::test_get_officials_by_zip_code", "tests/test_officials_api.py::TestOfficialsAPI::test_get_officials_empty", "tests/test_officials_api.py::TestOfficialsAPI::test_invalid_zip_code", "tests/test_officials_api.py::TestOfficialsAPI::test_pagination", "tests/test_officials_api.py::TestOfficialsAPI::test_search_officials_by_level", "tests/test_officials_api.py::TestOfficialsAPI::test_search_officials_by_name", "tests/test_officials_api.py::TestOfficialsAPI::test_update_official"]